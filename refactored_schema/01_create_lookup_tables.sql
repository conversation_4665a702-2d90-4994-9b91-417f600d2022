-- =====================================================
-- OXMA Database Refactored Schema - Lookup Tables
-- =====================================================
-- This file creates normalized lookup tables to replace
-- VARCHAR fields with proper foreign key relationships

-- Create categories table (normalized from categoria_producto)
CREATE TABLE IF NOT EXISTS categoria (
    id_categoria INT AUTO_INCREMENT PRIMARY KEY,
    nombre_categoria VARCHAR(50) NOT NULL UNIQUE,
    descripcion_categoria TEXT,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_categoria_nombre (nombre_categoria),
    INDEX idx_categoria_activo (activo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create sales status table (normalized from estado_venta)
CREATE TABLE IF NOT EXISTS estado_venta (
    id_estado_venta INT AUTO_INCREMENT PRIMARY KEY,
    nombre_estado VARCHAR(20) NOT NULL UNIQUE,
    descripcion_estado VARCHAR(100),
    es_final BOOLEAN DEFAULT FALSE COMMENT 'Indicates if this is a final state',
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_estado_nombre (nombre_estado),
    INDEX idx_estado_activo (activo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create document types table for better customer identification
CREATE TABLE IF NOT EXISTS tipo_documento (
    id_tipo_documento INT AUTO_INCREMENT PRIMARY KEY,
    codigo_tipo VARCHAR(5) NOT NULL UNIQUE,
    nombre_tipo VARCHAR(30) NOT NULL,
    descripcion TEXT,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tipo_codigo (codigo_tipo),
    INDEX idx_tipo_activo (activo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default categories based on existing data
INSERT INTO categoria (nombre_categoria, descripcion_categoria) VALUES
('Cadena', 'Cadenas de diferentes tipos y materiales'),
('Pulsera', 'Pulseras y brazaletes'),
('Topos', 'Aretes tipo topo'),
('Candonga', 'Aretes tipo candonga'),
('Personalizados', 'Productos personalizados'),
('Dijes', 'Dijes y colgantes'),
('Otros', 'Productos varios'),
('Aretas', 'Aretes diversos'),
('Earcuff', 'Aretes tipo earcuff'),
('Anillo', 'Anillos diversos'),
('Tobillera', 'Tobilleras'),
('Pandora', 'Productos estilo Pandora'),
('Cadena Individual', 'Cadenas individuales'),
('Juego Topo', 'Juegos de topos'),
('Juego Areta', 'Juegos de aretas'),
('Decoración', 'Artículos decorativos'),
('Prendedor', 'Prendedores'),
('Reloj', 'Relojes');

-- Insert default sales statuses
INSERT INTO estado_venta (nombre_estado, descripcion_estado, es_final) VALUES
('pendiente', 'Venta registrada pero pendiente de pago', FALSE),
('completa', 'Venta completada y pagada', TRUE),
('cancelada', 'Venta cancelada', TRUE),
('devuelta', 'Venta devuelta total o parcialmente', TRUE);

-- Insert default document types
INSERT INTO tipo_documento (codigo_tipo, nombre_tipo, descripcion) VALUES
('CC', 'Cédula de Ciudadanía', 'Documento de identidad colombiano'),
('CE', 'Cédula de Extranjería', 'Documento para extranjeros residentes'),
('PAS', 'Pasaporte', 'Documento de identidad internacional'),
('NIT', 'NIT', 'Número de Identificación Tributaria'),
('TI', 'Tarjeta de Identidad', 'Documento para menores de edad');
