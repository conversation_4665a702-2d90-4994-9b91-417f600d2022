-- =====================================================
-- OXMA Database Refactored Schema - Main Tables
-- =====================================================
-- This file creates the main business tables with improved
-- data types, constraints, and indexing

-- Improved proveedor table
CREATE TABLE IF NOT EXISTS proveedor_new (
    id_proveedor INT AUTO_INCREMENT PRIMARY KEY,
    codigo_proveedor VARCHAR(10) NOT NULL UNIQUE,
    nombre_proveedor VARCHAR(100) NOT NULL,
    contacto_proveedor VARCHAR(100),
    telefono_proveedor VARCHAR(15),
    email_proveedor VARCHAR(100),
    direccion_proveedor TEXT,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_proveedor_codigo (codigo_proveedor),
    INDEX idx_proveedor_nombre (nombre_proveedor),
    INDEX idx_proveedor_activo (activo),
    
    -- Constraints
    CONSTRAINT chk_proveedor_email CHECK (email_proveedor IS NULL OR email_proveedor REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_proveedor_telefono CHECK (telefono_proveedor IS NULL OR LENGTH(telefono_proveedor) >= 7)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved cliente table
CREATE TABLE IF NOT EXISTS cliente_new (
    id_cliente INT AUTO_INCREMENT PRIMARY KEY,
    id_tipo_documento INT NOT NULL,
    numero_documento VARCHAR(20) NOT NULL,
    nombre_cliente VARCHAR(100) NOT NULL,
    apellido_cliente VARCHAR(100),
    telefono_cliente VARCHAR(15),
    email_cliente VARCHAR(100),
    direccion_cliente TEXT,
    fecha_nacimiento DATE,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE KEY uk_cliente_documento (id_tipo_documento, numero_documento),
    INDEX idx_cliente_nombre (nombre_cliente),
    INDEX idx_cliente_apellido (apellido_cliente),
    INDEX idx_cliente_email (email_cliente),
    INDEX idx_cliente_telefono (telefono_cliente),
    INDEX idx_cliente_activo (activo),
    
    -- Foreign Keys
    FOREIGN KEY (id_tipo_documento) REFERENCES tipo_documento(id_tipo_documento),
    
    -- Constraints
    CONSTRAINT chk_cliente_email CHECK (email_cliente IS NULL OR email_cliente REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_cliente_telefono CHECK (telefono_cliente IS NULL OR LENGTH(telefono_cliente) >= 7),
    CONSTRAINT chk_cliente_fecha_nacimiento CHECK (fecha_nacimiento IS NULL OR fecha_nacimiento <= CURDATE())
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved producto table
CREATE TABLE IF NOT EXISTS producto_new (
    id_producto INT AUTO_INCREMENT PRIMARY KEY,
    codigo_producto VARCHAR(30) NOT NULL UNIQUE,
    nombre_producto VARCHAR(100) NOT NULL,
    descripcion_producto TEXT,
    id_categoria INT NOT NULL,
    cantidad_producto INT NOT NULL DEFAULT 0,
    cantidad_minima INT DEFAULT 0 COMMENT 'Minimum stock level for alerts',
    costo_producto DECIMAL(12,2) NOT NULL,
    precio_producto DECIMAL(12,2) NOT NULL,
    margen_ganancia DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN costo_producto > 0 THEN ((precio_producto - costo_producto) / costo_producto) * 100
            ELSE 0 
        END
    ) STORED COMMENT 'Calculated profit margin percentage',
    id_proveedor INT NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_producto_codigo (codigo_producto),
    INDEX idx_producto_nombre (nombre_producto),
    INDEX idx_producto_categoria (id_categoria),
    INDEX idx_producto_proveedor (id_proveedor),
    INDEX idx_producto_activo (activo),
    INDEX idx_producto_cantidad (cantidad_producto),
    INDEX idx_producto_precio (precio_producto),
    
    -- Foreign Keys
    FOREIGN KEY (id_categoria) REFERENCES categoria(id_categoria),
    FOREIGN KEY (id_proveedor) REFERENCES proveedor_new(id_proveedor),
    
    -- Constraints
    CONSTRAINT chk_producto_cantidad CHECK (cantidad_producto >= 0),
    CONSTRAINT chk_producto_cantidad_minima CHECK (cantidad_minima >= 0),
    CONSTRAINT chk_producto_costo CHECK (costo_producto >= 0),
    CONSTRAINT chk_producto_precio CHECK (precio_producto >= 0),
    CONSTRAINT chk_producto_precio_mayor_costo CHECK (precio_producto >= costo_producto)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
