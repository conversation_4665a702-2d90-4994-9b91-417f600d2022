-- =====================================================
-- OXMA Database Refactored Schema - Detail Tables
-- =====================================================
-- This file creates detail/junction tables with improved
-- naming and structure

-- Improved venta_detalle table (replaces productos_por_venta)
CREATE TABLE IF NOT EXISTS venta_detalle (
    id_venta_detalle INT AUTO_INCREMENT PRIMARY KEY,
    id_venta INT NOT NULL,
    id_producto INT NOT NULL,
    cantidad DECIMAL(10,3) NOT NULL,
    precio_unitario DECIMAL(12,2) NOT NULL,
    descuento_porcentaje DECIMAL(5,2) DEFAULT 0.00,
    descuento_valor DECIMAL(12,2) DEFAULT 0.00,
    subtotal DECIMAL(15,2) GENERATED ALWAYS AS (
        (cantidad * precio_unitario) - descuento_valor
    ) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE KEY uk_venta_producto (id_venta, id_producto),
    INDEX idx_venta_detalle_venta (id_venta),
    INDEX idx_venta_detalle_producto (id_producto),
    INDEX idx_venta_detalle_cantidad (cantidad),
    INDEX idx_venta_detalle_precio (precio_unitario),
    
    -- Foreign Keys
    FOREIGN KEY (id_venta) REFERENCES venta_new(id_venta) ON DELETE CASCADE,
    FOREIGN KEY (id_producto) REFERENCES producto_new(id_producto),
    
    -- Constraints
    CONSTRAINT chk_venta_detalle_cantidad CHECK (cantidad > 0),
    CONSTRAINT chk_venta_detalle_precio CHECK (precio_unitario >= 0),
    CONSTRAINT chk_venta_detalle_descuento_porcentaje CHECK (descuento_porcentaje >= 0 AND descuento_porcentaje <= 100),
    CONSTRAINT chk_venta_detalle_descuento_valor CHECK (descuento_valor >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved compra_detalle table (replaces productos_por_compra)
CREATE TABLE IF NOT EXISTS compra_detalle (
    id_compra_detalle INT AUTO_INCREMENT PRIMARY KEY,
    id_compra INT NOT NULL,
    id_producto INT NOT NULL,
    cantidad DECIMAL(10,3) NOT NULL,
    costo_unitario DECIMAL(12,2) NOT NULL,
    descuento_porcentaje DECIMAL(5,2) DEFAULT 0.00,
    descuento_valor DECIMAL(12,2) DEFAULT 0.00,
    subtotal DECIMAL(15,2) GENERATED ALWAYS AS (
        (cantidad * costo_unitario) - descuento_valor
    ) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE KEY uk_compra_producto (id_compra, id_producto),
    INDEX idx_compra_detalle_compra (id_compra),
    INDEX idx_compra_detalle_producto (id_producto),
    INDEX idx_compra_detalle_cantidad (cantidad),
    INDEX idx_compra_detalle_costo (costo_unitario),
    
    -- Foreign Keys
    FOREIGN KEY (id_compra) REFERENCES compra_new(id_compra) ON DELETE CASCADE,
    FOREIGN KEY (id_producto) REFERENCES producto_new(id_producto),
    
    -- Constraints
    CONSTRAINT chk_compra_detalle_cantidad CHECK (cantidad > 0),
    CONSTRAINT chk_compra_detalle_costo CHECK (costo_unitario >= 0),
    CONSTRAINT chk_compra_detalle_descuento_porcentaje CHECK (descuento_porcentaje >= 0 AND descuento_porcentaje <= 100),
    CONSTRAINT chk_compra_detalle_descuento_valor CHECK (descuento_valor >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved devolucion_detalle table (replaces productos_por_devolucion)
CREATE TABLE IF NOT EXISTS devolucion_detalle (
    id_devolucion_detalle INT AUTO_INCREMENT PRIMARY KEY,
    id_devolucion INT NOT NULL,
    id_producto INT NOT NULL,
    cantidad DECIMAL(10,3) NOT NULL,
    precio_unitario DECIMAL(12,2) NOT NULL,
    subtotal DECIMAL(15,2) GENERATED ALWAYS AS (cantidad * precio_unitario) STORED,
    motivo_detalle TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE KEY uk_devolucion_producto (id_devolucion, id_producto),
    INDEX idx_devolucion_detalle_devolucion (id_devolucion),
    INDEX idx_devolucion_detalle_producto (id_producto),
    INDEX idx_devolucion_detalle_cantidad (cantidad),
    
    -- Foreign Keys
    FOREIGN KEY (id_devolucion) REFERENCES devolucion_new(id_devolucion) ON DELETE CASCADE,
    FOREIGN KEY (id_producto) REFERENCES producto_new(id_producto),
    
    -- Constraints
    CONSTRAINT chk_devolucion_detalle_cantidad CHECK (cantidad > 0),
    CONSTRAINT chk_devolucion_detalle_precio CHECK (precio_unitario >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved pago table (replaces medio_pago_venta)
CREATE TABLE IF NOT EXISTS pago (
    id_pago INT AUTO_INCREMENT PRIMARY KEY,
    id_venta INT NOT NULL,
    id_medio_pago INT NOT NULL,
    monto DECIMAL(15,2) NOT NULL,
    numero_referencia VARCHAR(50),
    fecha_pago TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    observaciones TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_pago_venta (id_venta),
    INDEX idx_pago_medio (id_medio_pago),
    INDEX idx_pago_fecha (fecha_pago),
    INDEX idx_pago_monto (monto),
    INDEX idx_pago_referencia (numero_referencia),
    
    -- Foreign Keys
    FOREIGN KEY (id_venta) REFERENCES venta_new(id_venta) ON DELETE CASCADE,
    FOREIGN KEY (id_medio_pago) REFERENCES medio_pago(id_medio_pago),
    
    -- Constraints
    CONSTRAINT chk_pago_monto CHECK (monto > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
