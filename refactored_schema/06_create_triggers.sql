-- =====================================================
-- OXMA Database Refactored Schema - Triggers
-- =====================================================
-- This file creates triggers for automatic inventory
-- management and data validation

DELIMITER $$

-- Trigger to update inventory after sale
CREATE TRIGGER tr_venta_detalle_after_insert
AFTER INSERT ON venta_detalle
FOR EACH ROW
BEGIN
    DECLARE current_quantity DECIMAL(10,3);
    DECLARE new_quantity DECIMAL(10,3);
    
    -- Get current quantity
    SELECT cantidad_producto INTO current_quantity 
    FROM producto_new 
    WHERE id_producto = NEW.id_producto;
    
    -- Calculate new quantity
    SET new_quantity = current_quantity - NEW.cantidad;
    
    -- Update product quantity
    UPDATE producto_new 
    SET cantidad_producto = new_quantity,
        updated_at = CURRENT_TIMESTAMP
    WHERE id_producto = NEW.id_producto;
    
    -- Insert inventory movement record
    INSERT INTO inventario_movimiento (
        id_producto, tipo_movimiento, cantidad, cantidad_anterior, 
        cantidad_nueva, costo_unitario, tipo_documento, id_documento, motivo
    ) VALUES (
        NEW.id_producto, 'salida', -NEW.cantidad, current_quantity, 
        new_quantity, NEW.precio_unitario, 'venta', NEW.id_venta, 
        CONCAT('Venta detalle ID: ', NEW.id_venta_detalle)
    );
    
    -- Check for low stock alert
    IF new_quantity <= (SELECT COALESCE(cantidad_minima, 0) FROM producto_new WHERE id_producto = NEW.id_producto) THEN
        INSERT INTO inventario_alerta (
            id_producto, tipo_alerta, mensaje, cantidad_actual, cantidad_minima
        ) 
        SELECT 
            NEW.id_producto, 
            CASE 
                WHEN new_quantity <= 0 THEN 'stock_agotado'
                ELSE 'stock_bajo'
            END,
            CASE 
                WHEN new_quantity <= 0 THEN CONCAT('Producto agotado: ', p.nombre_producto)
                ELSE CONCAT('Stock bajo para producto: ', p.nombre_producto)
            END,
            new_quantity,
            p.cantidad_minima
        FROM producto_new p 
        WHERE p.id_producto = NEW.id_producto;
    END IF;
END$$

-- Trigger to update inventory after purchase
CREATE TRIGGER tr_compra_detalle_after_insert
AFTER INSERT ON compra_detalle
FOR EACH ROW
BEGIN
    DECLARE current_quantity DECIMAL(10,3);
    DECLARE new_quantity DECIMAL(10,3);
    
    -- Get current quantity
    SELECT cantidad_producto INTO current_quantity 
    FROM producto_new 
    WHERE id_producto = NEW.id_producto;
    
    -- Calculate new quantity
    SET new_quantity = current_quantity + NEW.cantidad;
    
    -- Update product quantity and cost
    UPDATE producto_new 
    SET cantidad_producto = new_quantity,
        costo_producto = NEW.costo_unitario,
        updated_at = CURRENT_TIMESTAMP
    WHERE id_producto = NEW.id_producto;
    
    -- Insert inventory movement record
    INSERT INTO inventario_movimiento (
        id_producto, tipo_movimiento, cantidad, cantidad_anterior, 
        cantidad_nueva, costo_unitario, tipo_documento, id_documento, motivo
    ) VALUES (
        NEW.id_producto, 'entrada', NEW.cantidad, current_quantity, 
        new_quantity, NEW.costo_unitario, 'compra', NEW.id_compra, 
        CONCAT('Compra detalle ID: ', NEW.id_compra_detalle)
    );
END$$

-- Trigger to update totals in venta table
CREATE TRIGGER tr_venta_detalle_update_totals
AFTER INSERT ON venta_detalle
FOR EACH ROW
BEGIN
    UPDATE venta_new 
    SET subtotal = (
        SELECT COALESCE(SUM(subtotal), 0) 
        FROM venta_detalle 
        WHERE id_venta = NEW.id_venta
    ),
    total = subtotal - descuento_valor + impuestos,
    updated_at = CURRENT_TIMESTAMP
    WHERE id_venta = NEW.id_venta;
END$$

-- Trigger to update totals in compra table
CREATE TRIGGER tr_compra_detalle_update_totals
AFTER INSERT ON compra_detalle
FOR EACH ROW
BEGIN
    UPDATE compra_new 
    SET subtotal = (
        SELECT COALESCE(SUM(subtotal), 0) 
        FROM compra_detalle 
        WHERE id_compra = NEW.id_compra
    ),
    total = subtotal - descuento + impuestos,
    updated_at = CURRENT_TIMESTAMP
    WHERE id_compra = NEW.id_compra;
END$$

-- Trigger to generate automatic sale numbers
CREATE TRIGGER tr_venta_before_insert
BEFORE INSERT ON venta_new
FOR EACH ROW
BEGIN
    IF NEW.numero_venta IS NULL THEN
        SET NEW.numero_venta = CONCAT('V', YEAR(NOW()), LPAD(MONTH(NOW()), 2, '0'), '-', LPAD((
            SELECT COALESCE(MAX(CAST(SUBSTRING(numero_venta, 8) AS UNSIGNED)), 0) + 1
            FROM venta_new 
            WHERE numero_venta LIKE CONCAT('V', YEAR(NOW()), LPAD(MONTH(NOW()), 2, '0'), '-%')
        ), 6, '0'));
    END IF;
END$$

-- Trigger to generate automatic purchase numbers
CREATE TRIGGER tr_compra_before_insert
BEFORE INSERT ON compra_new
FOR EACH ROW
BEGIN
    IF NEW.numero_compra IS NULL THEN
        SET NEW.numero_compra = CONCAT('C', YEAR(NOW()), LPAD(MONTH(NOW()), 2, '0'), '-', LPAD((
            SELECT COALESCE(MAX(CAST(SUBSTRING(numero_compra, 8) AS UNSIGNED)), 0) + 1
            FROM compra_new 
            WHERE numero_compra LIKE CONCAT('C', YEAR(NOW()), LPAD(MONTH(NOW()), 2, '0'), '-%')
        ), 6, '0'));
    END IF;
END$$

DELIMITER ;
