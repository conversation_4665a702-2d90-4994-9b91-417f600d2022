-- =====================================================
-- OXMA Database Refactored Schema - Transaction Tables
-- =====================================================
-- This file creates transaction tables (venta, compra, etc.)
-- with improved data types and constraints

-- Improved venta table
CREATE TABLE IF NOT EXISTS venta_new (
    id_venta INT AUTO_INCREMENT PRIMARY KEY,
    numero_venta VARCHAR(20) UNIQUE COMMENT 'Human-readable sale number',
    fecha_venta TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    id_cliente INT NOT NULL,
    id_estado_venta INT NOT NULL DEFAULT 1,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    descuento_porcentaje DECIMAL(5,2) DEFAULT 0.00,
    descuento_valor DECIMAL(15,2) DEFAULT 0.00,
    impuestos DECIMAL(15,2) DEFAULT 0.00,
    total DECIMAL(15,2) NOT NULL,
    observaciones TEXT,
    vendedor VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_venta_numero (numero_venta),
    INDEX idx_venta_fecha (fecha_venta),
    INDEX idx_venta_cliente (id_cliente),
    INDEX idx_venta_estado (id_estado_venta),
    INDEX idx_venta_total (total),
    INDEX idx_venta_vendedor (vendedor),
    INDEX idx_venta_fecha_estado (fecha_venta, id_estado_venta),
    
    -- Foreign Keys
    FOREIGN KEY (id_cliente) REFERENCES cliente_new(id_cliente),
    FOREIGN KEY (id_estado_venta) REFERENCES estado_venta(id_estado_venta),
    
    -- Constraints
    CONSTRAINT chk_venta_subtotal CHECK (subtotal >= 0),
    CONSTRAINT chk_venta_descuento_porcentaje CHECK (descuento_porcentaje >= 0 AND descuento_porcentaje <= 100),
    CONSTRAINT chk_venta_descuento_valor CHECK (descuento_valor >= 0),
    CONSTRAINT chk_venta_impuestos CHECK (impuestos >= 0),
    CONSTRAINT chk_venta_total CHECK (total >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved compra table
CREATE TABLE IF NOT EXISTS compra_new (
    id_compra INT AUTO_INCREMENT PRIMARY KEY,
    numero_compra VARCHAR(20) UNIQUE COMMENT 'Human-readable purchase number',
    fecha_compra TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    id_proveedor INT NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    descuento DECIMAL(15,2) DEFAULT 0.00,
    impuestos DECIMAL(15,2) DEFAULT 0.00,
    total DECIMAL(15,2) NOT NULL,
    estado_compra ENUM('pendiente', 'recibida', 'cancelada') DEFAULT 'pendiente',
    observaciones TEXT,
    numero_factura_proveedor VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_compra_numero (numero_compra),
    INDEX idx_compra_fecha (fecha_compra),
    INDEX idx_compra_proveedor (id_proveedor),
    INDEX idx_compra_estado (estado_compra),
    INDEX idx_compra_total (total),
    INDEX idx_compra_factura_proveedor (numero_factura_proveedor),
    
    -- Foreign Keys
    FOREIGN KEY (id_proveedor) REFERENCES proveedor_new(id_proveedor),
    
    -- Constraints
    CONSTRAINT chk_compra_subtotal CHECK (subtotal >= 0),
    CONSTRAINT chk_compra_descuento CHECK (descuento >= 0),
    CONSTRAINT chk_compra_impuestos CHECK (impuestos >= 0),
    CONSTRAINT chk_compra_total CHECK (total >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved devolucion table
CREATE TABLE IF NOT EXISTS devolucion_new (
    id_devolucion INT AUTO_INCREMENT PRIMARY KEY,
    numero_devolucion VARCHAR(20) UNIQUE COMMENT 'Human-readable return number',
    fecha_devolucion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    id_venta INT COMMENT 'Original sale if return is from a sale',
    id_proveedor INT COMMENT 'Supplier if return is to supplier',
    tipo_devolucion ENUM('cliente', 'proveedor') NOT NULL,
    motivo_devolucion TEXT NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total DECIMAL(15,2) NOT NULL,
    estado_devolucion ENUM('pendiente', 'procesada', 'cancelada') DEFAULT 'pendiente',
    observaciones TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_devolucion_numero (numero_devolucion),
    INDEX idx_devolucion_fecha (fecha_devolucion),
    INDEX idx_devolucion_venta (id_venta),
    INDEX idx_devolucion_proveedor (id_proveedor),
    INDEX idx_devolucion_tipo (tipo_devolucion),
    INDEX idx_devolucion_estado (estado_devolucion),
    
    -- Foreign Keys
    FOREIGN KEY (id_venta) REFERENCES venta_new(id_venta),
    FOREIGN KEY (id_proveedor) REFERENCES proveedor_new(id_proveedor),
    
    -- Constraints
    CONSTRAINT chk_devolucion_subtotal CHECK (subtotal >= 0),
    CONSTRAINT chk_devolucion_total CHECK (total >= 0),
    CONSTRAINT chk_devolucion_referencia CHECK (
        (tipo_devolucion = 'cliente' AND id_venta IS NOT NULL AND id_proveedor IS NULL) OR
        (tipo_devolucion = 'proveedor' AND id_proveedor IS NOT NULL AND id_venta IS NULL)
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
