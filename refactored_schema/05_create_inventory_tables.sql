-- =====================================================
-- OXMA Database Refactored Schema - Inventory Tables
-- =====================================================
-- This file creates improved inventory management tables

-- Improved recuento table (inventory count)
CREATE TABLE IF NOT EXISTS inventario_recuento (
    id_recuento INT AUTO_INCREMENT PRIMARY KEY,
    numero_recuento VARCHAR(20) UNIQUE COMMENT 'Human-readable count number',
    fecha_recuento TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    tipo_recuento ENUM('completo', 'parcial', 'ciclico') DEFAULT 'completo',
    id_usuario INT COMMENT 'User who performed the count',
    estado_recuento ENUM('en_proceso', 'completado', 'cancelado') DEFAULT 'en_proceso',
    observaciones TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_recuento_numero (numero_recuento),
    INDEX idx_recuento_fecha (fecha_recuento),
    INDEX idx_recuento_tipo (tipo_recuento),
    INDEX idx_recuento_estado (estado_recuento),
    INDEX idx_recuento_usuario (id_usuario),
    
    -- Constraints
    CONSTRAINT chk_recuento_fecha CHECK (fecha_recuento <= NOW())
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Improved inventario_recuento_detalle table (replaces productos_por_recuento)
CREATE TABLE IF NOT EXISTS inventario_recuento_detalle (
    id_recuento_detalle INT AUTO_INCREMENT PRIMARY KEY,
    id_recuento INT NOT NULL,
    id_producto INT NOT NULL,
    cantidad_sistema DECIMAL(10,3) NOT NULL COMMENT 'Quantity according to system',
    cantidad_fisica DECIMAL(10,3) NOT NULL COMMENT 'Physical count quantity',
    diferencia DECIMAL(10,3) GENERATED ALWAYS AS (cantidad_fisica - cantidad_sistema) STORED,
    costo_unitario DECIMAL(12,2) NOT NULL,
    valor_diferencia DECIMAL(15,2) GENERATED ALWAYS AS (diferencia * costo_unitario) STORED,
    observaciones TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE KEY uk_recuento_producto (id_recuento, id_producto),
    INDEX idx_recuento_detalle_recuento (id_recuento),
    INDEX idx_recuento_detalle_producto (id_producto),
    INDEX idx_recuento_detalle_diferencia (diferencia),
    INDEX idx_recuento_detalle_valor_diferencia (valor_diferencia),
    
    -- Foreign Keys
    FOREIGN KEY (id_recuento) REFERENCES inventario_recuento(id_recuento) ON DELETE CASCADE,
    FOREIGN KEY (id_producto) REFERENCES producto_new(id_producto),
    
    -- Constraints
    CONSTRAINT chk_recuento_detalle_cantidad_sistema CHECK (cantidad_sistema >= 0),
    CONSTRAINT chk_recuento_detalle_cantidad_fisica CHECK (cantidad_fisica >= 0),
    CONSTRAINT chk_recuento_detalle_costo CHECK (costo_unitario >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- New inventory movement table for better tracking
CREATE TABLE IF NOT EXISTS inventario_movimiento (
    id_movimiento INT AUTO_INCREMENT PRIMARY KEY,
    id_producto INT NOT NULL,
    tipo_movimiento ENUM('entrada', 'salida', 'ajuste', 'transferencia') NOT NULL,
    cantidad DECIMAL(10,3) NOT NULL,
    cantidad_anterior DECIMAL(10,3) NOT NULL,
    cantidad_nueva DECIMAL(10,3) NOT NULL,
    costo_unitario DECIMAL(12,2),
    documento_referencia VARCHAR(50) COMMENT 'Reference to source document',
    tipo_documento ENUM('venta', 'compra', 'devolucion', 'recuento', 'ajuste') NOT NULL,
    id_documento INT COMMENT 'ID of the source document',
    motivo TEXT,
    id_usuario INT COMMENT 'User who made the movement',
    fecha_movimiento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_movimiento_producto (id_producto),
    INDEX idx_movimiento_tipo (tipo_movimiento),
    INDEX idx_movimiento_fecha (fecha_movimiento),
    INDEX idx_movimiento_documento (tipo_documento, id_documento),
    INDEX idx_movimiento_usuario (id_usuario),
    INDEX idx_movimiento_producto_fecha (id_producto, fecha_movimiento),
    
    -- Foreign Keys
    FOREIGN KEY (id_producto) REFERENCES producto_new(id_producto),
    
    -- Constraints
    CONSTRAINT chk_movimiento_cantidad CHECK (cantidad != 0),
    CONSTRAINT chk_movimiento_cantidad_anterior CHECK (cantidad_anterior >= 0),
    CONSTRAINT chk_movimiento_cantidad_nueva CHECK (cantidad_nueva >= 0),
    CONSTRAINT chk_movimiento_costo CHECK (costo_unitario IS NULL OR costo_unitario >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- New table for inventory alerts
CREATE TABLE IF NOT EXISTS inventario_alerta (
    id_alerta INT AUTO_INCREMENT PRIMARY KEY,
    id_producto INT NOT NULL,
    tipo_alerta ENUM('stock_bajo', 'stock_agotado', 'stock_negativo', 'diferencia_recuento') NOT NULL,
    mensaje TEXT NOT NULL,
    cantidad_actual DECIMAL(10,3),
    cantidad_minima DECIMAL(10,3),
    fecha_alerta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estado_alerta ENUM('activa', 'resuelta', 'ignorada') DEFAULT 'activa',
    fecha_resolucion TIMESTAMP NULL,
    observaciones TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_alerta_producto (id_producto),
    INDEX idx_alerta_tipo (tipo_alerta),
    INDEX idx_alerta_fecha (fecha_alerta),
    INDEX idx_alerta_estado (estado_alerta),
    INDEX idx_alerta_producto_estado (id_producto, estado_alerta),
    
    -- Foreign Keys
    FOREIGN KEY (id_producto) REFERENCES producto_new(id_producto),
    
    -- Constraints
    CONSTRAINT chk_alerta_cantidad_actual CHECK (cantidad_actual IS NULL OR cantidad_actual >= 0),
    CONSTRAINT chk_alerta_cantidad_minima CHECK (cantidad_minima IS NULL OR cantidad_minima >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
