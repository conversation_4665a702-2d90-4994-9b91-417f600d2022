-- =====================================================
-- OXMA Database Migration - Step 2: Migrate Data
-- =====================================================
-- This script migrates data from old tables to new tables
-- with proper data type conversions and normalization

-- First, run the schema creation scripts
-- SOURCE refactored_schema/01_create_lookup_tables.sql;
-- SOURCE refactored_schema/02_create_main_tables.sql;
-- SOURCE refactored_schema/03_create_transaction_tables.sql;
-- SOURCE refactored_schema/04_create_detail_tables.sql;
-- SOURCE refactored_schema/05_create_inventory_tables.sql;

-- Migrate proveedor data
INSERT INTO proveedor_new (
    id_proveedor, codigo_proveedor, nombre_proveedor, activo, created_at, updated_at
)
SELECT 
    id_proveedor,
    codigo_proveedor,
    nombre_proveedor,
    TRUE,
    NOW(),
    NOW()
FROM backup_proveedor;

-- Migrate cliente data with document type normalization
INSERT INTO cliente_new (
    id_cliente, id_tipo_documento, numero_documento, nombre_cliente, 
    telefono_cliente, email_cliente, activo, created_at, updated_at
)
SELECT 
    id_cliente,
    1, -- Default to 'CC' (Cédula de Ciudadanía)
    cedula_cliente,
    nombre_cliente,
    telefono_cliente,
    CASE 
        WHEN correo_cliente = '' OR correo_cliente IS NULL THEN NULL
        ELSE correo_cliente
    END,
    TRUE,
    NOW(),
    NOW()
FROM backup_cliente;

-- Migrate producto data with category normalization and decimal conversion
INSERT INTO producto_new (
    id_producto, codigo_producto, nombre_producto, id_categoria, 
    cantidad_producto, costo_producto, precio_producto, id_proveedor, 
    activo, created_at, updated_at
)
SELECT 
    p.id_producto,
    p.codigo_producto,
    p.nombre_producto,
    c.id_categoria,
    p.cantidad_producto,
    p.costo_producto / 100.0, -- Convert from integer cents to decimal
    p.precio_producto / 100.0, -- Convert from integer cents to decimal
    p.id_proveedor,
    TRUE,
    NOW(),
    NOW()
FROM backup_producto p
JOIN categoria c ON c.nombre_categoria = p.categoria_producto;

-- Migrate venta data with decimal conversion
INSERT INTO venta_new (
    id_venta, fecha_venta, id_cliente, id_estado_venta, 
    subtotal, descuento_valor, total, observaciones, created_at, updated_at
)
SELECT 
    v.id_venta,
    v.fecha_venta,
    v.id_cliente,
    CASE 
        WHEN v.estado_venta = 'completa' THEN 2
        WHEN v.estado_venta = 'cancelada' THEN 3
        WHEN v.estado_venta = 'devuelta' THEN 4
        ELSE 1 -- pendiente
    END,
    v.valor_bruto_venta / 100.0, -- Convert from integer cents to decimal
    COALESCE(v.descuento_venta, 0) / 100.0, -- Convert from integer cents to decimal
    v.valor_total_venta / 100.0, -- Convert from integer cents to decimal
    v.observacion_venta,
    NOW(),
    NOW()
FROM backup_venta v;

-- Migrate productos_por_venta to venta_detalle
INSERT INTO venta_detalle (
    id_venta, id_producto, cantidad, precio_unitario, created_at, updated_at
)
SELECT 
    pv.id_venta,
    pv.id_producto,
    pv.cantidad_producto,
    pv.precio_producto / 100.0, -- Convert from integer cents to decimal
    NOW(),
    NOW()
FROM backup_productos_por_venta pv;

-- Migrate compra data (if exists)
INSERT INTO compra_new (
    id_compra, fecha_compra, id_proveedor, total, estado_compra, created_at, updated_at
)
SELECT 
    id_compra,
    fecha_compra,
    id_proveedor,
    total_compra / 100.0, -- Assuming total_compra exists and is in cents
    'recibida', -- Default status
    NOW(),
    NOW()
FROM backup_compra
WHERE fecha_compra IS NOT NULL;

-- Migrate medio_pago_venta to pago
INSERT INTO pago (
    id_venta, id_medio_pago, monto, fecha_pago, created_at, updated_at
)
SELECT 
    mpv.id_venta,
    mpv.id_medio_pago,
    mpv.valor_medio_pago / 100.0, -- Convert from integer cents to decimal
    v.fecha_venta, -- Use sale date as payment date
    NOW(),
    NOW()
FROM backup_medio_pago_venta mpv
JOIN backup_venta v ON v.id_venta = mpv.id_venta;
