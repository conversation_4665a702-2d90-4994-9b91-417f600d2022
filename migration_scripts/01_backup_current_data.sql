-- =====================================================
-- OXMA Database Migration - Step 1: Backup Current Data
-- =====================================================
-- This script creates backup tables of current data
-- before starting the migration process

-- Create backup tables with current data
CREATE TABLE IF NOT EXISTS backup_cliente AS SELECT * FROM cliente;
CREATE TABLE IF NOT EXISTS backup_producto AS SELECT * FROM producto;
CREATE TABLE IF NOT EXISTS backup_proveedor AS SELECT * FROM proveedor;
CREATE TABLE IF NOT EXISTS backup_venta AS SELECT * FROM venta;
CREATE TABLE IF NOT EXISTS backup_compra AS SELECT * FROM compra;
CREATE TABLE IF NOT EXISTS backup_devolucion AS SELECT * FROM devolucion;
CREATE TABLE IF NOT EXISTS backup_medio_pago AS SELECT * FROM medio_pago;
CREATE TABLE IF NOT EXISTS backup_productos_por_venta AS SELECT * FROM productos_por_venta;
CREATE TABLE IF NOT EXISTS backup_productos_por_compra AS SELECT * FROM productos_por_compra;
CREATE TABLE IF NOT EXISTS backup_productos_por_devolucion AS SELECT * FROM productos_por_devolucion;
CREATE TABLE IF NOT EXISTS backup_medio_pago_venta AS SELECT * FROM medio_pago_venta;
CREATE TABLE IF NOT EXISTS backup_recuento AS SELECT * FROM recuento;
CREATE TABLE IF NOT EXISTS backup_productos_por_recuento AS SELECT * FROM productos_por_recuento;
CREATE TABLE IF NOT EXISTS backup_medio_pago_recuento AS SELECT * FROM medio_pago_recuento;
CREATE TABLE IF NOT EXISTS backup_factura AS SELECT * FROM factura;

-- Verify backup counts
SELECT 
    'cliente' as tabla, COUNT(*) as registros FROM backup_cliente
UNION ALL
SELECT 
    'producto' as tabla, COUNT(*) as registros FROM backup_producto
UNION ALL
SELECT 
    'proveedor' as tabla, COUNT(*) as registros FROM backup_proveedor
UNION ALL
SELECT 
    'venta' as tabla, COUNT(*) as registros FROM backup_venta
UNION ALL
SELECT 
    'compra' as tabla, COUNT(*) as registros FROM backup_compra
UNION ALL
SELECT 
    'devolucion' as tabla, COUNT(*) as registros FROM backup_devolucion
UNION ALL
SELECT 
    'medio_pago' as tabla, COUNT(*) as registros FROM backup_medio_pago
UNION ALL
SELECT 
    'productos_por_venta' as tabla, COUNT(*) as registros FROM backup_productos_por_venta
UNION ALL
SELECT 
    'productos_por_compra' as tabla, COUNT(*) as registros FROM backup_productos_por_compra
UNION ALL
SELECT 
    'productos_por_devolucion' as tabla, COUNT(*) as registros FROM backup_productos_por_devolucion
UNION ALL
SELECT 
    'medio_pago_venta' as tabla, COUNT(*) as registros FROM backup_medio_pago_venta
UNION ALL
SELECT 
    'recuento' as tabla, COUNT(*) as registros FROM backup_recuento
UNION ALL
SELECT 
    'productos_por_recuento' as tabla, COUNT(*) as registros FROM backup_productos_por_recuento
UNION ALL
SELECT 
    'medio_pago_recuento' as tabla, COUNT(*) as registros FROM backup_medio_pago_recuento
UNION ALL
SELECT 
    'factura' as tabla, COUNT(*) as registros FROM backup_factura;
